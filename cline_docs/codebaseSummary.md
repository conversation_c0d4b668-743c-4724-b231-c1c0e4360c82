# 代码库概览

## 项目概述
这是一个基于Vue 3 + Vuetify的订单报告管理系统，主要用于查看和筛选订单数据。

## 关键组件及其交互

### 主要页面组件
- **OrderReport3.vue** - 订单报告主页面
  - 包含订单筛选功能
  - 城市选择器（支持全选功能）
  - 订单列表展示
  - 订单详情弹窗

### 数据流
1. 用户在筛选区域选择条件
2. 城市选择器通过watch监听器处理全选逻辑
3. 点击查询按钮触发数据获取
4. 订单列表展示筛选结果
5. 点击订单项打开详情弹窗

## 外部依赖
- **Vue 3** - 核心框架
- **Vuetify 3** - UI组件库
- **Vite** - 构建工具

## 最近重要变更

### 城市全选功能修复 (2025-06-23)
**问题**: 城市选择中的全选选项没有实际功能
**解决方案**:
1. 添加了`watch`监听器监听`selectedCities`变化
2. 实现了完整的全选逻辑：
   - 点击全选自动选择所有城市
   - 取消全选自动取消所有城市
   - 手动选择所有城市自动勾选全选
   - 取消任意城市自动取消全选

**技术实现**:
```javascript
// 获取除了"全选"之外的所有城市值
const getAllCityValues = () => {
  return cities.value.filter(city => city.value !== '全选').map(city => city.value);
};

// 监听城市选择变化，处理全选逻辑
watch(selectedCities, (newValue, oldValue) => {
  // 全选逻辑处理...
}, { deep: true });
```

## 用户反馈集成
当前修复直接响应了用户关于"城市选项中的全选没有效果"的反馈，提升了用户体验。

## 代码质量
- 使用TypeScript确保类型安全
- 遵循Vue 3 Composition API最佳实践
- 代码结构清晰，易于维护
