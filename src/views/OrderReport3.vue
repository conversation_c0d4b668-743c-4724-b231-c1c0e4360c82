<template>
  <v-container fluid class="pa-3" style="max-width: 1400px;">
    <!-- 顶部筛选区域 -->
    <v-card class="mb-3" elevation="1">
      <v-card-text class="py-3">
        <v-row align="center" class="mb-2">
          <v-col cols="12" md="4">
            <v-select
              v-model="selectedBrand"
              label="当前品牌"
              :items="brands"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
        </v-row>
        
        <v-divider class="my-2" />
        
        <div class="text-subtitle-1 mb-2 font-weight-bold">订单筛选</div>
        
        <v-row class="mb-2">
          <v-col cols="12" md="2">
            <v-text-field
              v-model="startDate"
              label="起始"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-text-field
              v-model="endDate"
              label="结束"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-checkbox
              v-model="last24Hours"
              label="24小时内的订单"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="3">
            <v-radio-group v-model="timeType" inline density="compact" hide-details>
              <v-radio label="按下单时间" value="order"></v-radio>
              <v-radio label="按取货时间" value="pickup"></v-radio>
            </v-radio-group>
          </v-col>
        </v-row>

        <v-row class="mb-3">
          <v-col cols="12">
            <div class="text-body-2 mb-2 font-weight-medium">类型 Status</div>
            <v-chip-group v-model="selectedStatus" mandatory class="flex-wrap">
              <v-chip
                v-for="status in orderStatuses"
                :key="status.value"
                :value="status.value"
                color="primary"
                variant="outlined"
                size="small"
              >
                {{ status.label }}
              </v-chip>
            </v-chip-group>
          </v-col>
        </v-row>

        <!-- 城市筛选区域 -->
        <v-row class="mb-3">
          <v-col cols="12">
            <div class="d-flex align-center mb-2">
              <div class="text-body-2 font-weight-medium mr-3">城市</div>
              <v-btn
                @click="cityExpanded = !cityExpanded"
                variant="text"
                size="small"
                color="primary"
                :prepend-icon="cityExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down'"
              >
                {{ cityExpanded ? '收起' : '展开' }}
              </v-btn>
            </div>
            <v-expand-transition>
              <div v-show="cityExpanded" class="city-checkbox-grid">
                <v-checkbox
                  v-for="city in cities"
                  :key="city.value"
                  v-model="selectedCities"
                  :value="city.value"
                  :label="city.label"
                  density="compact"
                  hide-details
                  class="city-checkbox"
                />
              </div>
            </v-expand-transition>
          </v-col>
        </v-row>

        <!-- 商家搜索 -->
        <v-row class="mb-2">
          <v-col cols="12">
            <div class="text-body-2 mb-2 font-weight-medium">商家 Search</div>
            <v-text-field
              v-model="merchantSearch"
              placeholder="搜索商家..."
              variant="outlined"
              density="compact"
              hide-details
              clearable
            />
          </v-col>
        </v-row>

        <!-- 来源和过滤选项 -->
        <v-row class="mb-2">
          <v-col cols="12" md="4">
            <div class="text-body-2 mb-2 font-weight-medium">来源 Source</div>
            <div class="d-flex flex-wrap">
              <v-checkbox
                v-model="selectedSources"
                value="FOODSUP"
                label="FOODSUP"
                density="compact"
                hide-details
                class="mr-4"
              />
              <v-checkbox
                v-model="selectedSources"
                value="FOODHWY"
                label="FOODHWY"
                density="compact"
                hide-details
              />
            </div>
          </v-col>
          <v-col cols="12" md="3">
            <div class="text-body-2 mb-2 font-weight-medium">过滤</div>
            <v-checkbox
              v-model="selfPickup"
              label="自取"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="5" class="d-flex align-center justify-end">
            <v-btn color="success" class="mr-2" @click="searchOrders" size="default">
              查询 Refresh
            </v-btn>
            <v-checkbox
              v-model="autoRefresh"
              label="自动刷新 Auto refresh"
              density="compact"
              hide-details
              class="mr-3"
            />
            <v-checkbox
              v-model="showDetails"
              label="显示明细"
              density="compact"
              hide-details
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 简化的订单列表 -->
    <v-card
      v-for="order in orders"
      :key="order.id"
      class="mb-2"
      elevation="1"
      hover
      @click="openOrderDetail(order)"
      style="cursor: pointer;"
    >
      <v-card-text class="pa-3">
        <v-row align="center">
          <v-col cols="12" md="2" class="d-flex align-center">
            <span class="text-h6 mr-2">#{{ order.id }}</span>
            <v-chip
              v-if="order.urgent"
              color="red"
              size="x-small"
              class="ml-1"
            >
              紧急
            </v-chip>
          </v-col>
          
          <v-col cols="12" md="3">
            <div class="text-body-2 font-weight-bold">{{ order.store.name }}</div>
            <div class="text-caption text-grey">{{ order.store.phone }}</div>
          </v-col>
          
          <v-col cols="12" md="2">
            <div class="text-caption text-grey">来源</div>
            <div class="text-body-2 font-weight-medium">{{ order.source }}</div>
          </v-col>
          
          <v-col cols="12" md="2">
            <div class="text-caption text-grey">下单时间</div>
            <div class="text-body-2">{{ order.order_time }}</div>
          </v-col>
          
          <v-col cols="12" md="2">
            <div class="text-h6 text-primary font-weight-bold">{{ order.total }}</div>
          </v-col>
          
          <v-col cols="12" md="1" class="text-right">
            <v-btn
              icon="mdi-chevron-right"
              variant="text"
              size="small"
              color="primary"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 分页 -->
    <v-card class="mt-3" elevation="0">
      <v-card-text class="text-center py-2">
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          :total-visible="5"
          size="small"
        />
      </v-card-text>
    </v-card>

    <!-- 订单详情弹窗 -->
    <v-dialog v-model="detailDialog" max-width="1200px" scrollable persistent>
      <v-card class="order-detail-dialog">
        <!-- 标题栏 -->
        <v-card-title class="order-header px-6 py-4">
          <div class="d-flex align-center justify-space-between w-100">
            <div class="d-flex align-center">
              <v-icon icon="mdi-receipt" color="primary" class="mr-3" size="large" />
              <div>
                <div class="text-h5 font-weight-bold text-primary">
                  订单详情 #{{ selectedOrder?.order_number }}
                </div>
                <div class="text-caption text-grey-darken-1">
                  Order Details - {{ selectedOrder?.source }}
                </div>
              </div>
            </div>
            <div class="d-flex align-center">
              <v-chip
                :color="getStatusColor(selectedOrder?.status)"
                variant="flat"
                size="large"
                class="mr-2"
              >
                <v-icon start icon="mdi-circle" size="small" />
                {{ getStatusText(selectedOrder?.status) }}
              </v-chip>
              <v-chip
                v-if="selectedOrder?.urgent"
                color="error"
                variant="flat"
                size="large"
                class="mr-3"
              >
                <v-icon start icon="mdi-alert" size="small" />
                紧急
              </v-chip>
              <v-btn
                icon="mdi-close"
                variant="text"
                size="large"
                @click="detailDialog = false"
              />
            </div>
          </div>
        </v-card-title>

        <v-divider />

        <v-card-text class="pa-6">
          <!-- 顶部信息卡片组 -->
          <v-row class="mb-6">
            <!-- 订单基础信息 -->
            <v-col cols="12" md="4">
              <v-card variant="outlined" class="info-card h-100" elevation="0">
                <v-card-subtitle class="bg-primary-lighten text-primary font-weight-bold py-3 px-4">
                  <v-icon icon="mdi-clipboard-text" class="mr-2" />
                  订单信息
                </v-card-subtitle>
                <v-card-text class="pa-4">
                  <div class="info-item">
                    <div class="info-label">商家电话</div>
                    <div class="info-value">
                      <v-icon icon="mdi-phone" size="small" class="mr-1" />
                      {{ selectedOrder?.seller_phone }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">司机电话</div>
                    <div class="info-value">
                      <v-icon icon="mdi-cellphone" size="small" class="mr-1" />
                      {{ selectedOrder?.driver_phone }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">单号</div>
                    <div class="info-value">
                      <v-chip size="small" variant="outlined" color="primary">
                        {{ selectedOrder?.order_number }}
                      </v-chip>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">来源</div>
                    <div class="info-value">
                      <v-chip 
                        size="small" 
                        :color="getSourceColor(selectedOrder?.source)"
                        variant="flat"
                      >
                        {{ selectedOrder?.source }}
                      </v-chip>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">收款负责人</div>
                    <div class="info-value">
                      <v-icon icon="mdi-account" size="small" class="mr-1" />
                      {{ selectedOrder?.collector }}
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 时间信息 -->
            <v-col cols="12" md="4">
              <v-card variant="outlined" class="info-card h-100" elevation="0">
                <v-card-subtitle class="bg-success-lighten text-success font-weight-bold py-3 px-4">
                  <v-icon icon="mdi-clock-outline" class="mr-2" />
                  时间信息
                </v-card-subtitle>
                <v-card-text class="pa-4">
                  <div class="info-item">
                    <div class="info-label">下单时间</div>
                    <div class="info-value">
                      <v-icon icon="mdi-calendar-clock" size="small" class="mr-1" />
                      {{ selectedOrder?.order_time }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">预计送达时间</div>
                    <div class="info-value">
                      <v-icon icon="mdi-truck-delivery" size="small" class="mr-1" />
                      {{ selectedOrder?.delivery_time }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">本次配送日期</div>
                    <div class="info-value">
                      <v-icon icon="mdi-calendar" size="small" class="mr-1" />
                      {{ selectedOrder?.delivery_date || '待确认' }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">配送状态</div>
                    <div class="info-value">
                      <v-chip 
                        size="small" 
                        color="info"
                        variant="flat"
                      >
                        <v-icon start icon="mdi-truck" size="small" />
                        {{ selectedOrder?.delivery_status || 'TEST delivery' }}
                      </v-chip>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 商家信息 -->
            <v-col cols="12" md="4">
              <v-card variant="outlined" class="info-card h-100" elevation="0">
                <v-card-subtitle class="bg-warning-lighten text-warning-darken-2 font-weight-bold py-3 px-4">
                  <v-icon icon="mdi-store" class="mr-2" />
                  商家信息
                </v-card-subtitle>
                <v-card-text class="pa-4">
                  <div class="info-item">
                    <div class="info-label">商家名称</div>
                    <div class="info-value font-weight-bold">{{ selectedOrder?.store.name }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">联系电话</div>
                    <div class="info-value">
                      <v-icon icon="mdi-phone" size="small" class="mr-1" />
                      {{ selectedOrder?.store.phone }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">商家地址</div>
                    <div class="info-value">
                      <v-icon icon="mdi-map-marker" size="small" class="mr-1" />
                      {{ selectedOrder?.store.address }}
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">[中]会员编号</div>
                    <div class="info-value">
                      <v-chip size="small" variant="outlined" color="secondary">
                        {{ selectedOrder?.store.member_id }}
                      </v-chip>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <!-- 订单明细表格 -->
          <v-card variant="outlined" elevation="0" class="mb-6">
            <v-card-subtitle class="bg-info-lighten text-info font-weight-bold py-3 px-4">
              <v-icon icon="mdi-format-list-bulleted" class="mr-2" />
              订单明细
              <v-chip size="small" variant="outlined" color="info" class="ml-2">
                共 {{ selectedOrder?.items?.length || 0 }} 项
              </v-chip>
            </v-card-subtitle>
            
            <v-table class="order-detail-table" density="comfortable">
              <thead>
                <tr class="table-header">
                  <th class="text-left font-weight-bold text-grey-darken-2 pa-4">项目</th>
                  <th class="text-center font-weight-bold text-grey-darken-2 pa-4" style="width: 120px;">数量</th>
                  <th class="text-right font-weight-bold text-grey-darken-2 pa-4" style="width: 120px;">金额</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in selectedOrder?.items" :key="index" class="table-row">
                  <td class="pa-4">
                    <div class="d-flex align-center">
                      <div>
                        <div class="font-weight-medium text-grey-darken-2">{{ item.name }}</div>
                        <div v-if="item.code" class="text-caption text-grey">编号: {{ item.code }}</div>
                        <div v-if="item.note" class="text-caption text-warning">{{ item.note }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="text-center pa-4">
                    <v-chip 
                      v-if="item.quantity"
                      size="small" 
                      variant="outlined"
                      color="primary"
                    >
                      {{ item.quantity }}
                    </v-chip>
                  </td>
                  <td class="text-right pa-4">
                    <div class="font-weight-bold text-h6 text-primary">{{ item.price }}</div>
                  </td>
                </tr>
              </tbody>
            </v-table>

            <!-- 费用汇总 -->
            <v-divider />
            <div class="pa-4 bg-grey-lighten-5">
              <v-row class="summary-section">
                <v-col cols="12" md="8">
                  <div class="d-flex flex-column">
                    <div class="summary-item d-flex justify-space-between">
                      <span class="text-body-2">菜价小计:</span>
                      <span class="font-weight-medium">{{ selectedOrder?.subtotal || '$425.00' }}</span>
                    </div>
                    <div class="summary-item d-flex justify-space-between">
                      <span class="text-body-2 text-warning">菜价小计 (税后):</span>
                      <span class="font-weight-medium">$425</span>
                    </div>
                    <div class="summary-item d-flex justify-space-between">
                      <span class="text-body-2">配送服务费 (无法计费):</span>
                      <span class="font-weight-medium text-success">$0.00</span>
                    </div>
                    <div class="summary-item d-flex justify-space-between">
                      <span class="text-body-2">司机运费:</span>
                      <span class="font-weight-medium">$0.00</span>
                    </div>
                    <div class="summary-item d-flex justify-space-between">
                      <span class="text-body-2">税:</span>
                      <span class="font-weight-medium">$0.00</span>
                    </div>
                    <div class="summary-item d-flex justify-space-between">
                      <span class="text-body-2">小费:</span>
                      <span class="font-weight-medium">$0</span>
                    </div>
                    <div class="summary-item d-flex justify-space-between">
                      <span class="text-body-2">押金:</span>
                      <span class="font-weight-medium">$0.00</span>
                    </div>
                  </div>
                </v-col>
                <v-col cols="12" md="4" class="d-flex align-end justify-end">
                  <div class="total-section">
                    <div class="d-flex align-center justify-space-between total-row">
                      <span class="text-h6 font-weight-bold">总计:</span>
                      <span class="text-h5 font-weight-bold text-primary">{{ selectedOrder?.total }}</span>
                    </div>
                    <div class="text-caption text-grey text-right mt-1">
                      2份 · 共计{{ selectedOrder?.total || '$0.00' }}元
                    </div>
                  </div>
                </v-col>
              </v-row>
            </div>
          </v-card>

          <!-- 备注区域 -->
          <v-row>
            <v-col cols="12" md="6">
              <v-card variant="outlined" elevation="0">
                <v-card-subtitle class="bg-purple-lighten-4 text-purple-darken-2 font-weight-bold py-3 px-4">
                  <v-icon icon="mdi-note-text" class="mr-2" />
                  减免备注
                </v-card-subtitle>
                <v-card-text class="pa-4">
                  <v-select
                    label="请选择补偿类型"
                    :items="discountTypes"
                    variant="outlined"
                    density="compact"
                    class="mb-3"
                  />
                  <v-textarea
                    v-model="discountNote"
                    placeholder="[取消] [return.] 2箱 冷货; 原始单号: 23714473; ( [测试] 桃子 20LB| [Test] Peach 20LB| [Test] Pêche 20LBX1, [测试] 桃子 20LB x 20| [Test] Peach 20LB| [Test] Pêche 20LBX1)"
                    variant="outlined"
                    rows="4"
                    hide-details
                    class="text-body-2"
                  />
                  <v-btn color="primary" variant="outlined" class="mt-3" size="small">
                    提交
                  </v-btn>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" md="6">
              <v-card variant="outlined" elevation="0">
                <v-card-subtitle class="bg-teal-lighten-4 text-teal-darken-2 font-weight-bold py-3 px-4">
                  <v-icon icon="mdi-comment-text" class="mr-2" />
                  后台订单备注
                </v-card-subtitle>
                <v-card-text class="pa-4">
                  <v-textarea
                    v-model="backendNote"
                    placeholder="请输入后台订单备注..."
                    variant="outlined"
                    rows="6"
                    hide-details
                    class="text-body-2"
                  />
                  <v-btn color="primary" variant="outlined" class="mt-3" size="small">
                    提交
                  </v-btn>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>

        <v-divider />

        <!-- 操作按钮区域 -->
        <v-card-actions class="action-buttons pa-6">
          <div class="d-flex align-center justify-space-between w-100">
            <div class="d-flex align-center">
              <v-btn
                color="primary"
                variant="flat"
                prepend-icon="mdi-pencil"
                class="mr-3"
                size="large"
              >
                修改记录
              </v-btn>
              <v-btn
                color="info"
                variant="outlined"
                prepend-icon="mdi-plus"
                class="mr-3"
                size="large"
              >
                补充记录
              </v-btn>
              <v-btn
                color="warning"
                variant="outlined"
                prepend-icon="mdi-email"
                class="mr-3"
                size="large"
              >
                发送Invoice
              </v-btn>
            </div>
            <div class="d-flex align-center">
              <v-btn
                color="success"
                variant="flat"
                prepend-icon="mdi-check"
                class="mr-3"
                size="large"
              >
                客服记录
              </v-btn>
              <v-btn
                color="error"
                variant="outlined"
                prepend-icon="mdi-cancel"
                class="mr-3"
                size="large"
              >
                推送消息给司机
              </v-btn>
              <v-btn
                color="grey"
                variant="outlined"
                prepend-icon="mdi-close"
                @click="detailDialog = false"
                size="large"
              >
                关闭
              </v-btn>
            </div>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { ref, watch } from 'vue';

// 筛选条件
const selectedBrand = ref('821: Foodhwy');
const startDate = ref('2025-06-19');
const endDate = ref('2025-06-19');
const last24Hours = ref(true);
const timeType = ref('order');
const selectedStatus = ref(0);
const selectedCities = ref([
  '全选', 'Halifax', 'Midtown Toronto', 'Mississauga', 'St. Catharines',
  'Montréal', 'North York', 'Scarborough', 'Waterloo', 'London',
  'DT of Toronto', 'Markham', 'Hamilton', 'Ottawa', 'Richmond Hill',
  'Kingston', 'Windsor', 'Guelph', 'Newmarket', 'Innisfil',
  'Orillia', 'Brantford', 'Vaughan', 'Winnipeg', 'Supply Chain',
  'Richmond BC', 'Aurora', 'Fairview', 'Oakville', 'Saskatoon',
  'Prince Edward Island', 'Etobicoke'
]);
const merchantSearch = ref('');
const selectedSources = ref(['FOODSUP', 'FOODHWY']);
const selfPickup = ref(false);
const autoRefresh = ref(false);
const showDetails = ref(false);
const cityExpanded = ref(false);
const currentPage = ref(1);
const totalPages = ref(5);

// 品牌选项
const brands = ref([
  '821: Foodhwy',
  '822: Other Brand'
]);

// 订单状态选项
const orderStatuses = ref([
  { label: '处理中 Processing', value: 0 },
  { label: '所有 All', value: 1 },
  { label: '新订单 New orders', value: 2 },
  { label: '已确认 Confirmed', value: 3 },
  { label: '已配送 Delivered', value: 4 },
  { label: '已完成 Finished', value: 5 },
  { label: '已取消 Cancelled', value: 6 },
  { label: '预订 Pre-order', value: 7 },
  { label: '自定义 Avg-order', value: 8 }
]);

// 城市选项
const cities = ref([
  { label: '全选', value: '全选' },
  { label: 'Halifax', value: 'Halifax' },
  { label: 'Midtown Toronto', value: 'Midtown Toronto' },
  { label: 'Mississauga', value: 'Mississauga' },
  { label: 'St. Catharines', value: 'St. Catharines' },
  { label: 'Montréal', value: 'Montréal' },
  { label: 'North York', value: 'North York' },
  { label: 'Scarborough', value: 'Scarborough' },
  { label: 'Waterloo', value: 'Waterloo' },
  { label: 'London', value: 'London' },
  { label: 'DT of Toronto', value: 'DT of Toronto' },
  { label: 'Markham', value: 'Markham' },
  { label: 'Hamilton', value: 'Hamilton' },
  { label: 'Ottawa', value: 'Ottawa' },
  { label: 'Richmond Hill', value: 'Richmond Hill' },
  { label: 'Kingston', value: 'Kingston' },
  { label: 'Windsor', value: 'Windsor' },
  { label: 'Guelph', value: 'Guelph' },
  { label: 'Newmarket', value: 'Newmarket' },
  { label: 'Innisfil', value: 'Innisfil' },
  { label: 'Orillia', value: 'Orillia' },
  { label: 'Brantford', value: 'Brantford' },
  { label: 'Vaughan', value: 'Vaughan' },
  { label: 'Winnipeg', value: 'Winnipeg' },
  { label: 'Supply Chain', value: 'Supply Chain' },
  { label: 'Richmond BC', value: 'Richmond BC' },
  { label: 'Aurora', value: 'Aurora' },
  { label: 'Fairview', value: 'Fairview' },
  { label: 'Oakville', value: 'Oakville' },
  { label: 'Saskatoon', value: 'Saskatoon' },
  { label: 'Prince Edward Island', value: 'Prince Edward Island' },
  { label: 'Etobicoke', value: 'Etobicoke' }
]);

// 备注相关数据
const discountNote = ref('[取消] [return.] 2箱 冷货; 原始单号: 23714473; ( [测试] 桃子 20LB| [Test] Peach 20LB| [Test] Pêche 20LBX1, [测试] 桃子 20LB x 20| [Test] Peach 20LB| [Test] Pêche 20LBX1)');
const backendNote = ref('');

// 减免类型选项
const discountTypes = ref([
  '正常',
  '部分退款',
  '全额退款',
  '商品缺货',
  '配送问题',
  '质量问题',
  '客户取消',
  '其他'
]);

// 订单数据
const orders = ref([
  {
    id: 5,
    version: { name: 'Ver: 2.1.5', color: 'success' },
    urgent: true,
    status: 'processing',
    seller_phone: '9999999999',
    driver_phone: '9991111111',
    order_number: '23714474',
    source: 'FOODSUP',
    collector: '司机 修改',
    order_time: '06/19 20:57',
    delivery_time: '预计送达时间: ',
    delivery_date: '修改配送时间',
    delivery_status: 'TEST delivery',
    store: {
      name: 'Northern Store, 称呼: tt, 电话: +18887786666, 地址: Baker Lake, NU X0C 0A0, Canada',
      phone: '+18887786666',
      address: 'Baker Lake, NU X0C 0A0, Canada',
      member_id: '01527424, 第次下单',
    },
    items: [
      { 
        name: '[测试] 桃子 20LB | [Test] Peach 20LB',
        code: 'C047739901675',
        quantity: 'x 1',
        price: '$25.00',
        note: '不符合期望'
      },
      { 
        name: '[测试] 桃子 20LB x 20 | [Test] Peach 20LB',
        code: 'C047739901675-w',
        quantity: 'x 1',
        price: '$400.00',
        note: '不符合期望'
      }
    ],
    subtotal: '$425.00',
    total: '$425.00'
  },
  {
    id: 6,
    version: { name: 'Ver: 2.1.5', color: 'success' },
    urgent: false,
    status: 'confirmed',
    seller_phone: '8888888888',
    driver_phone: '8881111111',
    order_number: '23714444',
    source: 'UBEREATS',
    collector: '商家',
    order_time: '06/18 03:15',
    delivery_time: '04:30 - 04:45',
    delivery_date: '06/18',
    delivery_status: 'UBER delivery',
    store: {
      name: 'Central Warehouse',
      phone: '18887786667',
      address: 'Yellowknife, NT X1A 0A1, Canada',
      member_id: '01527425, 第15次下单',
    },
    items: [
      { name: '新鲜蔬菜套餐', quantity: 'x 3', price: '$89.99' },
      { name: '配送费', quantity: '', price: '$15.00' },
      { name: '服务费', quantity: '', price: '$8.99' },
    ],
    subtotal: '$89.99',
    total: '$113.98'
  },
  {
    id: 7,
    version: { name: 'Ver: 2.1.5', color: 'success' },
    urgent: false,
    status: 'delivered',
    seller_phone: '7777777777',
    driver_phone: '7771111111',
    order_number: '23714445',
    source: 'DOORDASH',
    collector: '司机',
    order_time: '06/18 04:22',
    delivery_time: '05:15 - 05:30',
    delivery_date: '06/18',
    delivery_status: 'DOOR delivery',
    store: {
      name: 'Quick Bites',
      phone: '18887786668',
      address: 'Whitehorse, YT Y1A 0A2, Canada',
      member_id: '01527426, 第8次下单',
    },
    items: [
      { name: '招牌汉堡套餐', quantity: 'x 2', price: '$45.98' },
      { name: '可乐(大)', quantity: 'x 2', price: '$8.00' },
      { name: '薯条', quantity: 'x 1', price: '$6.99' },
    ],
    subtotal: '$45.98',
    total: '$60.97'
  }
]);

// 查询订单
const searchOrders = () => {
  console.log('查询订单');
  // 实际项目中这里会调用API
};

// 新增弹窗相关状态
const detailDialog = ref(false);
const selectedOrder = ref(null);

// 打开订单详情
const openOrderDetail = (order) => {
  selectedOrder.value = order;
  detailDialog.value = true;
};

// 获取订单状态颜色
const getStatusColor = (status) => {
  const statusColors = {
    'processing': 'warning',
    'confirmed': 'info',
    'delivered': 'success',
    'cancelled': 'error',
    'pending': 'grey'
  };
  return statusColors[status] || 'primary';
};

// 获取订单状态文本
const getStatusText = (status) => {
  const statusTexts = {
    'processing': '处理中',
    'confirmed': '已确认',
    'delivered': '已送达',
    'cancelled': '已取消',
    'pending': '待处理'
  };
  return statusTexts[status] || '未知状态';
};

// 获取来源颜色
const getSourceColor = (source) => {
  const sourceColors = {
    'FOODSUP': 'success',
    'FOODHWY': 'primary',
    'UBEREATS': 'orange',
    'DOORDASH': 'red',
    'GRUBHUB': 'warning'
  };
  return sourceColors[source] || 'grey';
};

// 获取除了"全选"之外的所有城市值
const getAllCityValues = () => {
  return cities.value.filter(city => city.value !== '全选').map(city => city.value);
};

// 监听城市选择变化，处理全选逻辑
watch(selectedCities, (newValue, oldValue) => {
  const allCityValues = getAllCityValues();
  const hasSelectAll = newValue.includes('全选');
  const hasAllCities = allCityValues.every(city => newValue.includes(city));
  
  // 如果点击了全选
  if (hasSelectAll && !oldValue.includes('全选')) {
    // 选择所有城市
    selectedCities.value = ['全选', ...allCityValues];
  }
  // 如果取消了全选
  else if (!hasSelectAll && oldValue.includes('全选')) {
    // 取消选择所有城市
    selectedCities.value = [];
  }
  // 如果手动选择了所有城市，自动勾选全选
  else if (!hasSelectAll && hasAllCities && newValue.length === allCityValues.length) {
    selectedCities.value = ['全选', ...allCityValues];
  }
  // 如果取消了某个城市，自动取消全选
  else if (hasSelectAll && !hasAllCities) {
    selectedCities.value = newValue.filter(city => city !== '全选');
  }
}, { deep: true });
</script>

<style scoped>
.v-card {
  transition: all 0.2s ease;
}

.v-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.border {
  border: 1px solid #e0e0e0 !important;
}

.v-table th {
  background-color: #f8f9fa !important;
}

.v-chip-group {
  max-width: 100%;
  overflow-x: auto;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.city-checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 4px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.city-checkbox {
  margin: 0 !important;
}

.city-checkbox :deep(.v-input__control) {
  min-height: 24px !important;
}

.city-checkbox :deep(.v-selection-control) {
  min-height: 24px !important;
}

.city-checkbox :deep(.v-label) {
  font-size: 0.875rem;
  opacity: 0.87;
}

@media (max-width: 768px) {
  .v-container {
    padding: 8px !important;
    max-width: 100% !important;
  }
  
  .v-card-text {
    padding: 12px !important;
  }
}

@media (min-width: 1200px) {
  .v-container {
    max-width: 1200px !important;
  }
}

/* 订单详情弹窗样式 */
.order-detail-dialog {
  border-radius: 12px !important;
  overflow: hidden;
}

.order-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 2px solid #e3f2fd;
}

.info-card {
  border-radius: 8px !important;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.info-item {
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 0.875rem;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.order-detail-table {
  border-radius: 8px !important;
}

.table-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.table-header th {
  color: white !important;
  font-weight: 600 !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.table-row {
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: #f8f9fa !important;
}

.summary-section {
  background-color: #fafbfc;
  border-radius: 8px;
  padding: 16px;
}

.summary-item {
  padding: 4px 0;
  font-size: 0.875rem;
}

.total-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
  min-width: 200px;
}

.total-row {
  border-bottom: 1px solid rgba(255,255,255,0.3);
  padding-bottom: 8px;
}

.action-buttons {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-buttons .v-btn {
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.25px;
}

/* 背景色辅助类 */
.bg-primary-lighten {
  background-color: #e3f2fd !important;
}

.bg-success-lighten {
  background-color: #e8f5e8 !important;
}

.bg-warning-lighten {
  background-color: #fff3e0 !important;
}

.bg-info-lighten {
  background-color: #e1f5fe !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .order-detail-dialog {
    margin: 8px !important;
    max-width: calc(100vw - 16px) !important;
  }
  
  .info-card {
    margin-bottom: 16px;
  }
  
  .total-section {
    min-width: auto;
    width: 100%;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }
  
  .action-buttons .d-flex {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }
  
  .action-buttons .v-btn {
    width: 100%;
    margin: 0 !important;
  }
}

/* 滚动条美化 */
.v-dialog ::-webkit-scrollbar {
  width: 6px;
}

.v-dialog ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.v-dialog ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.v-dialog ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
