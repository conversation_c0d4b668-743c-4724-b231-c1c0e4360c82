<template>
  <v-container fluid class="pa-3" style="max-width: 1400px;">
    <!-- 顶部筛选区域 -->
    <v-card class="mb-3" elevation="1">
      <v-card-text class="py-3">
        <v-row align="center" class="mb-2">
          <v-col cols="12" md="4">
            <v-select
              v-model="selectedBrand"
              label="当前品牌"
              :items="brands"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
        </v-row>
        
        <v-divider class="my-2" />
        
        <div class="text-subtitle-1 mb-2 font-weight-bold">订单筛选</div>
        
        <v-row class="mb-2">
          <v-col cols="12" md="2">
            <v-text-field
              v-model="startDate"
              label="起始"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-text-field
              v-model="endDate"
              label="结束"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-checkbox
              v-model="last24Hours"
              label="24小时内的订单"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="3">
            <v-radio-group v-model="timeType" inline density="compact" hide-details>
              <v-radio label="按下单时间" value="order"></v-radio>
              <v-radio label="按取货时间" value="pickup"></v-radio>
            </v-radio-group>
          </v-col>
        </v-row>

        <v-row class="mb-3">
          <v-col cols="12">
            <div class="text-body-2 mb-2 font-weight-medium">类型 Status</div>
            <v-chip-group v-model="selectedStatus" mandatory class="flex-wrap">
              <v-chip
                v-for="status in orderStatuses"
                :key="status.value"
                :value="status.value"
                color="primary"
                variant="outlined"
                size="small"
              >
                {{ status.label }}
              </v-chip>
            </v-chip-group>
          </v-col>
        </v-row>

        <!-- 城市筛选区域 -->
        <v-row class="mb-3">
          <v-col cols="12">
            <div class="text-body-2 mb-2 font-weight-medium">城市</div>
            <div class="city-checkbox-grid">
              <v-checkbox
                v-for="city in cities"
                :key="city.value"
                v-model="selectedCities"
                :value="city.value"
                :label="city.label"
                density="compact"
                hide-details
                class="city-checkbox"
              />
            </div>
          </v-col>
        </v-row>

        <!-- 商家搜索 -->
        <v-row class="mb-2">
          <v-col cols="12">
            <div class="text-body-2 mb-2 font-weight-medium">商家 Search</div>
            <v-text-field
              v-model="merchantSearch"
              placeholder="搜索商家..."
              variant="outlined"
              density="compact"
              hide-details
              clearable
            />
          </v-col>
        </v-row>

        <!-- 来源和过滤选项 -->
        <v-row class="mb-2">
          <v-col cols="12" md="4">
            <div class="text-body-2 mb-2 font-weight-medium">来源 Source</div>
            <div class="d-flex flex-wrap">
              <v-checkbox
                v-model="selectedSources"
                value="FOODSUP"
                label="FOODSUP"
                density="compact"
                hide-details
                class="mr-4"
              />
              <v-checkbox
                v-model="selectedSources"
                value="FOODHWY"
                label="FOODHWY"
                density="compact"
                hide-details
              />
            </div>
          </v-col>
          <v-col cols="12" md="3">
            <div class="text-body-2 mb-2 font-weight-medium">过滤</div>
            <v-checkbox
              v-model="selfPickup"
              label="自取"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="5" class="d-flex align-center justify-end">
            <v-btn color="success" class="mr-2" @click="searchOrders" size="default">
              查询 Refresh
            </v-btn>
            <v-checkbox
              v-model="autoRefresh"
              label="自动刷新 Auto refresh"
              density="compact"
              hide-details
              class="mr-3"
            />
            <v-checkbox
              v-model="showDetails"
              label="显示明细"
              density="compact"
              hide-details
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 简化的订单列表 -->
    <v-card
      v-for="order in orders"
      :key="order.id"
      class="mb-2"
      elevation="1"
      hover
      @click="openOrderDetail(order)"
      style="cursor: pointer;"
    >
      <v-card-text class="pa-3">
        <v-row align="center">
          <v-col cols="12" md="2" class="d-flex align-center">
            <span class="text-h6 mr-2">#{{ order.id }}</span>
            <v-chip
              v-if="order.urgent"
              color="red"
              size="x-small"
              class="ml-1"
            >
              紧急
            </v-chip>
          </v-col>
          
          <v-col cols="12" md="3">
            <div class="text-body-2 font-weight-bold">{{ order.store.name }}</div>
            <div class="text-caption text-grey">{{ order.store.phone }}</div>
          </v-col>
          
          <v-col cols="12" md="2">
            <div class="text-caption text-grey">来源</div>
            <div class="text-body-2 font-weight-medium">{{ order.source }}</div>
          </v-col>
          
          <v-col cols="12" md="2">
            <div class="text-caption text-grey">下单时间</div>
            <div class="text-body-2">{{ order.order_time }}</div>
          </v-col>
          
          <v-col cols="12" md="2">
            <div class="text-h6 text-primary font-weight-bold">{{ order.total }}</div>
          </v-col>
          
          <v-col cols="12" md="1" class="text-right">
            <v-btn
              icon="mdi-chevron-right"
              variant="text"
              size="small"
              color="primary"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 分页 -->
    <v-card class="mt-3" elevation="0">
      <v-card-text class="text-center py-2">
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          :total-visible="5"
          size="small"
        />
      </v-card-text>
    </v-card>

    <!-- 订单详情弹窗 -->
    <v-dialog v-model="detailDialog" max-width="1000px" scrollable>
      <v-card>
        <v-card-title class="d-flex align-center justify-space-between bg-grey-lighten-5">
          <div class="d-flex align-center">
            <span class="text-h5 mr-3">订单详情 #{{ selectedOrder?.id }}</span>
            <v-chip
              :color="selectedOrder?.version.color"
              size="small"
              variant="flat"
            >
              {{ selectedOrder?.version.name }}
            </v-chip>
            <v-chip
              v-if="selectedOrder?.urgent"
              color="red"
              size="small"
              class="ml-2"
            >
              紧急
            </v-chip>
          </div>
          <v-btn
            icon="mdi-close"
            variant="text"
            @click="detailDialog = false"
          />
        </v-card-title>

        <v-card-text class="pa-4">
          <v-row>
            <!-- 左侧订单信息 -->
            <v-col cols="12" md="6">
              <v-card variant="outlined" class="h-100" elevation="0">
                <v-card-subtitle class="bg-blue-lighten-5 py-2">
                  订单信息
                </v-card-subtitle>
                <v-card-text class="pa-3">
                  <v-row dense>
                    <v-col cols="6">
                      <div class="text-caption text-grey">商家电话</div>
                      <div class="text-body-2 font-weight-medium">{{ selectedOrder?.seller_phone }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-grey">司机电话</div>
                      <div class="text-body-2 font-weight-medium">{{ selectedOrder?.driver_phone }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-grey">单号</div>
                      <div class="text-body-2 font-weight-medium">{{ selectedOrder?.order_number }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-grey">来源</div>
                      <div class="text-body-2 font-weight-medium">{{ selectedOrder?.source }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-grey">收款负责人</div>
                      <div class="text-body-2 font-weight-medium">{{ selectedOrder?.collector }}</div>
                    </v-col>
                    <v-col cols="6">
                      <div class="text-caption text-grey">下单时间</div>
                      <div class="text-body-2 font-weight-medium">{{ selectedOrder?.order_time }}</div>
                    </v-col>
                    <v-col cols="12">
                      <div class="text-caption text-grey">预计送达时间</div>
                      <div class="text-body-2 font-weight-medium">{{ selectedOrder?.delivery_time }}</div>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>

            <!-- 右侧商家信息 -->
            <v-col cols="12" md="6">
              <v-card variant="outlined" class="h-100" elevation="0">
                <v-card-subtitle class="bg-green-lighten-5 py-2">
                  商家信息
                </v-card-subtitle>
                <v-card-text class="pa-3">
                  <div class="mb-2">
                    <div class="text-caption text-grey">商家名称</div>
                    <div class="text-body-2 font-weight-medium">{{ selectedOrder?.store.name }}</div>
                  </div>
                  <div class="mb-2">
                    <div class="text-caption text-grey">电话</div>
                    <div class="text-body-2 font-weight-medium">{{ selectedOrder?.store.phone }}</div>
                  </div>
                  <div class="mb-2">
                    <div class="text-caption text-grey">地址</div>
                    <div class="text-body-2 font-weight-medium">{{ selectedOrder?.store.address }}</div>
                  </div>
                  <div>
                    <div class="text-caption text-grey">[中]会员编号</div>
                    <div class="text-body-2 font-weight-medium">{{ selectedOrder?.store.member_id }}</div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <!-- 订单明细表格 -->
          <v-divider class="my-4" />
          
          <div class="text-subtitle-1 mb-3 font-weight-bold">订单明细</div>
          
          <v-table density="compact" class="border">
            <thead>
              <tr class="bg-grey-lighten-4">
                <th class="text-left font-weight-bold">项目</th>
                <th class="text-center font-weight-bold">数量</th>
                <th class="text-right font-weight-bold">金额</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in selectedOrder?.items" :key="index">
                <td class="py-2">{{ item.name }}</td>
                <td class="text-center py-2">{{ item.quantity }}</td>
                <td class="text-right py-2 font-weight-medium">{{ item.price }}</td>
              </tr>
              <tr class="bg-blue-lighten-5">
                <td colspan="2" class="text-right font-weight-bold py-3">总计:</td>
                <td class="text-right font-weight-bold py-3 text-h6">{{ selectedOrder?.total }}</td>
              </tr>
            </tbody>
          </v-table>

          <!-- 备注区域 -->
          <v-row class="mt-4">
            <v-col cols="12" md="6">
              <v-textarea
                label="减免备注"
                variant="outlined"
                rows="3"
                density="compact"
                hide-details
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-textarea
                label="后台订单备注"
                variant="outlined"
                rows="3"
                density="compact"
                hide-details
              />
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn color="primary" variant="outlined" class="mr-2">
            编辑
          </v-btn>
          <v-btn color="success" variant="outlined" class="mr-2">
            确认
          </v-btn>
          <v-btn color="info" variant="outlined" class="mr-2">
            打印
          </v-btn>
          <v-btn color="warning" variant="outlined" class="mr-2">
            取消
          </v-btn>
          <v-btn color="grey" variant="outlined" @click="detailDialog = false">
            关闭
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { ref } from 'vue';

// 筛选条件
const selectedBrand = ref('821: Foodhwy');
const startDate = ref('2025-06-19');
const endDate = ref('2025-06-19');
const last24Hours = ref(true);
const timeType = ref('order');
const selectedStatus = ref(0);
const selectedCities = ref(['全选', 'Halifax', 'Midtown Toronto', 'Mississauga', 'St. Catharines']);
const merchantSearch = ref('');
const selectedSources = ref(['FOODSUP', 'FOODHWY']);
const selfPickup = ref(false);
const autoRefresh = ref(false);
const showDetails = ref(false);
const currentPage = ref(1);
const totalPages = ref(5);

// 品牌选项
const brands = ref([
  '821: Foodhwy',
  '822: Other Brand'
]);

// 订单状态选项
const orderStatuses = ref([
  { label: '处理中 Processing', value: 0 },
  { label: '所有 All', value: 1 },
  { label: '新订单 New orders', value: 2 },
  { label: '已确认 Confirmed', value: 3 },
  { label: '已配送 Delivered', value: 4 },
  { label: '已完成 Finished', value: 5 },
  { label: '已取消 Cancelled', value: 6 },
  { label: '预订 Pre-order', value: 7 },
  { label: '自定义 Avg-order', value: 8 }
]);

// 城市选项
const cities = ref([
  { label: '全选', value: '全选' },
  { label: 'Halifax', value: 'Halifax' },
  { label: 'Midtown Toronto', value: 'Midtown Toronto' },
  { label: 'Mississauga', value: 'Mississauga' },
  { label: 'St. Catharines', value: 'St. Catharines' },
  { label: 'Montréal', value: 'Montréal' },
  { label: 'North York', value: 'North York' },
  { label: 'Scarborough', value: 'Scarborough' },
  { label: 'Waterloo', value: 'Waterloo' },
  { label: 'London', value: 'London' },
  { label: 'DT of Toronto', value: 'DT of Toronto' },
  { label: 'Markham', value: 'Markham' },
  { label: 'Hamilton', value: 'Hamilton' },
  { label: 'Ottawa', value: 'Ottawa' },
  { label: 'Richmond Hill', value: 'Richmond Hill' },
  { label: 'Kingston', value: 'Kingston' },
  { label: 'Windsor', value: 'Windsor' },
  { label: 'Guelph', value: 'Guelph' },
  { label: 'Newmarket', value: 'Newmarket' },
  { label: 'Innisfil', value: 'Innisfil' },
  { label: 'Orillia', value: 'Orillia' },
  { label: 'Brantford', value: 'Brantford' },
  { label: 'Vaughan', value: 'Vaughan' },
  { label: 'Winnipeg', value: 'Winnipeg' },
  { label: 'Supply Chain', value: 'Supply Chain' },
  { label: 'Richmond BC', value: 'Richmond BC' },
  { label: 'Aurora', value: 'Aurora' },
  { label: 'Fairview', value: 'Fairview' },
  { label: 'Oakville', value: 'Oakville' },
  { label: 'Saskatoon', value: 'Saskatoon' },
  { label: 'Prince Edward Island', value: 'Prince Edward Island' },
  { label: 'Etobicoke', value: 'Etobicoke' }
]);

// 订单数据
const orders = ref([
  {
    id: 5,
    version: { name: 'Ver: 2.1.5', color: 'success' },
    urgent: true,
    seller_phone: '9999999999',
    driver_phone: '9991111111',
    order_number: '23714443',
    source: 'FOODSUP',
    collector: '司机',
    order_time: '06/18 02:41',
    delivery_time: '03:57 - 04:13',
    store: {
      name: 'Northern Store',
      phone: '18887786666',
      address: 'Baker Lake, NU X0C 0A0, Canada',
      member_id: '01527424, 第27次下单',
    },
    items: [
      { name: '测试WMS商品同步123', quantity: 'x 5', price: '$150.00' },
      { name: '堂食的小计', quantity: '', price: '$750.00' },
      { name: '堂食的小计(税后)', quantity: '', price: '$862.31' },
    ],
    total: '$862.31'
  },
  {
    id: 6,
    version: { name: 'Ver: 2.1.5', color: 'success' },
    urgent: false,
    seller_phone: '8888888888',
    driver_phone: '8881111111',
    order_number: '23714444',
    source: 'UBEREATS',
    collector: '商家',
    order_time: '06/18 03:15',
    delivery_time: '04:30 - 04:45',
    store: {
      name: 'Central Warehouse',
      phone: '18887786667',
      address: 'Yellowknife, NT X1A 0A1, Canada',
      member_id: '01527425, 第15次下单',
    },
    items: [
      { name: '新鲜蔬菜套餐', quantity: 'x 3', price: '$89.99' },
      { name: '配送费', quantity: '', price: '$15.00' },
      { name: '服务费', quantity: '', price: '$8.99' },
    ],
    total: '$113.98'
  },
  {
    id: 7,
    version: { name: 'Ver: 2.1.5', color: 'success' },
    urgent: false,
    seller_phone: '7777777777',
    driver_phone: '7771111111',
    order_number: '23714445',
    source: 'DOORDASH',
    collector: '司机',
    order_time: '06/18 04:22',
    delivery_time: '05:15 - 05:30',
    store: {
      name: 'Quick Bites',
      phone: '18887786668',
      address: 'Whitehorse, YT Y1A 0A2, Canada',
      member_id: '01527426, 第8次下单',
    },
    items: [
      { name: '招牌汉堡套餐', quantity: 'x 2', price: '$45.98' },
      { name: '可乐(大)', quantity: 'x 2', price: '$8.00' },
      { name: '薯条', quantity: 'x 1', price: '$6.99' },
    ],
    total: '$60.97'
  }
]);

// 查询订单
const searchOrders = () => {
  console.log('查询订单');
  // 实际项目中这里会调用API
};

// 新增弹窗相关状态
const detailDialog = ref(false);
const selectedOrder = ref(null);

// 打开订单详情
const openOrderDetail = (order) => {
  selectedOrder.value = order;
  detailDialog.value = true;
};
</script>

<style scoped>
.v-card {
  transition: all 0.2s ease;
}

.v-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.border {
  border: 1px solid #e0e0e0 !important;
}

.v-table th {
  background-color: #f8f9fa !important;
}

.v-chip-group {
  max-width: 100%;
  overflow-x: auto;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.city-checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 4px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.city-checkbox {
  margin: 0 !important;
}

.city-checkbox :deep(.v-input__control) {
  min-height: 24px !important;
}

.city-checkbox :deep(.v-selection-control) {
  min-height: 24px !important;
}

.city-checkbox :deep(.v-label) {
  font-size: 0.875rem;
  opacity: 0.87;
}

@media (max-width: 768px) {
  .v-container {
    padding: 8px !important;
    max-width: 100% !important;
  }
  
  .v-card-text {
    padding: 12px !important;
  }
}

@media (min-width: 1200px) {
  .v-container {
    max-width: 1200px !important;
  }
}
</style>
